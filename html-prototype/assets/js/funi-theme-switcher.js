// funi-theme-switcher.js
(function () {
  const THEME_KEY = 'funi-theme';
  const DARK_THEME_CLASS = 'dark-theme';

  function applyTheme(theme) {
    document.body.classList.toggle(DARK_THEME_CLASS, theme === 'dark');
  }

  function getPreferredTheme() {
    if (localStorage.getItem(THEME_KEY)) {
      return localStorage.getItem(THEME_KEY);
    }
    return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
  }

  window.toggleTheme = function () {
    const currentTheme = document.body.classList.contains(DARK_THEME_CLASS) ? 'dark' : 'light';
    const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
    localStorage.setItem(THEME_KEY, newTheme);
    applyTheme(newTheme);
  };

  // Apply theme on load
  applyTheme(getPreferredTheme());
})();
