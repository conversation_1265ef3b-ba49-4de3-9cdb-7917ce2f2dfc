document.addEventListener('DOMContentLoaded', () => {
  const contentContainer = document.querySelector('.funi-content-wrapper');

  if (!contentContainer) {
    console.error('Content container not found');
    return;
  }

  function highlightMenuItem(currentPath) {
    // Remove active class from all menu items
    document.querySelectorAll('.funi-menu-item').forEach(item => {
      item.classList.remove('funi-menu-item-active');
    });

    // Collapse all menu groups initially
    document.querySelectorAll('.funi-menu-group').forEach(group => {
      group.classList.add('collapsed');
      const menuList = group.querySelector('.funi-menu-list');
      if (menuList) {
        menuList.style.height = '0px';
      }
    });

    // Find the active menu item and its parent groups
    const activeLink = document.querySelector(`.funi-menu-link[href="#${currentPath}"]`);
    if (activeLink) {
      const activeItem = activeLink.closest('.funi-menu-item');
      if (activeItem) {
        activeItem.classList.add('funi-menu-item-active');

        // Expand parent menu groups
        let parentGroup = activeItem.closest('.funi-menu-group');
        while (parentGroup) {
          parentGroup.classList.remove('collapsed');
          const menuList = parentGroup.querySelector('.funi-menu-list');
          if (menuList) {
            menuList.style.height = 'auto'; // Allow natural height
          }
          parentGroup = parentGroup.parentElement.closest('.funi-menu-group');
        }
      }
    }
  }

  function loadContent() {
    let fullPath = window.location.hash.substring(1); // Remove '#'
    let pathSegments = fullPath.split('?')[0].split('/'); // Split by '/' and remove query params
    let queryParams = fullPath.split('?')[1] || ''; // Get query params string

    let basePath = '';
    let pageFileName = 'list.html'; // Default page file name

    console.log('loadContent called with fullPath:', fullPath);
    console.log('pathSegments:', pathSegments);

    if (pathSegments.length > 0 && pathSegments[0] === '') {
      // Handle root path, e.g., #/
      pathSegments.shift(); // Remove empty string from split
    }

    if (pathSegments.length === 0) {
      // No hash, show welcome page instead of loading default.html
      showWelcomePage();
      highlightMenuItem('');
      return;
    }

    // Determine the base path for the directory and the specific page file
    if (pathSegments.length > 0) {
      const lastSegment = pathSegments[pathSegments.length - 1];
      if (lastSegment === 'add-edit' || lastSegment === 'detail-review') {
        pageFileName = `${lastSegment}.html`;
        basePath = pathSegments.slice(0, -1).join('/'); // Remove the page file name from path segments
      } else {
        basePath = pathSegments.join('/');
        pageFileName = 'list.html'; // Default to list.html for other paths
      }
    }

    const pagePath = `pages/${basePath}/${pageFileName}`;
    console.log('Resolved pagePath:', pagePath);
    loadPageContent(pagePath, queryParams);
    highlightMenuItem(basePath ? `/${basePath}` : ''); // Highlight menu item based on the base path (e.g., /procurement-plan-management)
  }

  function loadPageContent(pagePath, queryParams = '') {
    console.log('Loading page content via XMLHttpRequest:', pagePath);
    
    // 使用XMLHttpRequest来加载页面内容
    const xhr = new XMLHttpRequest();
    
    xhr.onreadystatechange = function() {
      if (xhr.readyState === 4) {
        if (xhr.status === 200) {
          try {
            const htmlContent = xhr.responseText;
            console.log('Successfully loaded page content');
            
            // 解析HTML内容
            const parser = new DOMParser();
            const doc = parser.parseFromString(htmlContent, 'text/html');
            
            // 提取body内容
            const bodyContent = doc.body ? doc.body.innerHTML : '';
            
            // 提取并加载CSS文件
            loadPageCSS(doc, pagePath);
            
            // 更新内容容器
            if (contentContainer) {
              contentContainer.innerHTML = bodyContent;
            } else {
              console.error('Content container not available for update');
              return;
            }
            
            // 应用当前主题
            syncThemeWithContent();
            
            // 强制样式重新计算
            setTimeout(() => {
              if (contentContainer) {
                contentContainer.style.display = 'none';
                contentContainer.offsetHeight; // 触发重排
                contentContainer.style.display = '';
              }
            }, 50);
            
            // 设置全局标志表示我们在路由模式下
            window.FUNI_ROUTER_MODE = true;
            console.log('Set FUNI_ROUTER_MODE to true');
            
            // 执行加载内容中的脚本
            executeScripts(contentContainer);
            
            // 调用页面初始化函数（如果可用）
            setTimeout(() => {
              if (window.initializePage && typeof window.initializePage === 'function') {
                window.initializePage();
              }
            }, 100);
            
            // 如果需要，将查询参数传递给加载的页面
            if (queryParams && window.loadedPageInit) {
              const params = new URLSearchParams(queryParams);
              window.loadedPageInit(params);
            }
            
          } catch (error) {
            console.error('Error processing loaded page:', error);
            showErrorPage(pagePath, error.message);
          }
        } else {
          console.error('Failed to load page:', pagePath, 'Status:', xhr.status);
          showErrorPage(pagePath, `HTTP ${xhr.status}`);
        }
      }
    };
    
    xhr.onerror = function() {
      console.error('Network error loading page:', pagePath);
      showErrorPage(pagePath, 'Network Error');
    };
    
    // 发送请求
    try {
      xhr.open('GET', pagePath, true);
      xhr.send();
    } catch (error) {
      console.error('Error initiating request:', error);
      showErrorPage(pagePath, error.message);
    }
  }
  
  // 显示欢迎页面的函数
  function showWelcomePage() {
    if (contentContainer) {
      contentContainer.innerHTML = `
        <div style="padding: 40px; text-align: center; color: var(--funi-text-color, #333);">
          <h2 style="color: var(--funi-primary-color, #007FFF); margin-bottom: 20px;">🎉 欢迎使用 FUNI 原型系统</h2>
          <p style="font-size: 16px; margin-bottom: 30px; color: var(--funi-text-secondary, #666);">请从左侧菜单选择要查看的功能模块</p>
          <div style="background: var(--funi-bg-secondary, #f8f9fa); padding: 20px; border-radius: 8px; max-width: 600px; margin: 0 auto;">
            <h3 style="margin-bottom: 15px;">📋 系统功能</h3>
            <div style="text-align: left;">
              <p>• 📊 数据管理和查看</p>
              <p>• 📝 表单录入和编辑</p>
              <p>• 🔍 高级搜索和筛选</p>
              <p>• 📈 数据统计和分析</p>
              <p>• ⚙️ 系统配置管理</p>
            </div>
          </div>
        </div>
      `;
    }
  }
  
  // 显示错误页面的辅助函数
  function showErrorPage(pagePath, errorInfo) {
    if (contentContainer) {
      contentContainer.innerHTML = '<div style="padding: 20px; text-align: center; color: var(--funi-danger-color);"><h3>页面加载失败</h3><p>无法加载页面: ' + pagePath + '</p><p>错误信息: ' + errorInfo + '</p><p>请确保页面文件存在且路径正确</p></div>';
    }
  }
  
  // 从页面加载CSS文件的函数
  function loadPageCSS(doc, pagePath) {
    // 获取页面中的所有link元素
    var linkElements = doc.querySelectorAll('link[rel="stylesheet"]');
    var pageDir = pagePath.substring(0, pagePath.lastIndexOf('/') + 1);
    
    console.log('Loading CSS for page:', pagePath);
    console.log('Found CSS links:', linkElements.length);

    for (var i = 0; i < linkElements.length; i++) {
      var link = linkElements[i];
      var href = link.getAttribute('href');
      if (href && !isAbsoluteURL(href)) {
        // 将相对路径转换为绝对路径
        var absoluteHref = resolveRelativePath(pageDir, href);
        console.log('Resolved CSS path:', href, '->', absoluteHref);

        // 检查是否已经加载过这个CSS
        var existingLink = document.querySelector('link[href="' + absoluteHref + '"]');
        if (!existingLink) {
          // 创建并添加新的link元素
          var newLink = document.createElement('link');
          newLink.rel = 'stylesheet';
          newLink.href = absoluteHref;
          newLink.setAttribute('data-page-css', 'true');
          document.head.appendChild(newLink);
          console.log('Added CSS link:', absoluteHref);
        } else {
          console.log('CSS already loaded:', absoluteHref);
        }
      }
    }
  }

  function isAbsoluteURL(url) {
    return /^https?:\/\//.test(url) || url.startsWith('/');
  }

  function resolveRelativePath(basePath, relativePath) {
    // Handle relative paths like ../../assets/css/file.css
    // basePath is like "pages/procurement-plan-management/"
    // relativePath is like "../../assets/css/funi-list.css"

    // Remove the filename from basePath if it exists
    let baseDir = basePath;
    if (!baseDir.endsWith('/')) {
      baseDir = baseDir.substring(0, baseDir.lastIndexOf('/') + 1);
    }

    // Split paths and filter out empty segments
    var baseSegments = baseDir.split('/').filter(function(segment) { return segment; });
    var relativeSegments = relativePath.split('/').filter(function(segment) { return segment; });

    var resultSegments = baseSegments.slice(); // 使用slice()复制数组

    for (var i = 0; i < relativeSegments.length; i++) {
      var segment = relativeSegments[i];
      if (segment === '..') {
        resultSegments.pop();
      } else if (segment !== '.') {
        resultSegments.push(segment);
      }
    }

    return resultSegments.join('/');
  }

  // Function to synchronize theme with loaded content
  function syncThemeWithContent() {
    const isDarkTheme = document.body.classList.contains('dark-theme');
    const contentBody = contentContainer.querySelector('#app') || contentContainer;
    if (contentBody) {
      if (isDarkTheme) {
        contentBody.classList.add('dark-theme');
      } else {
        contentBody.classList.remove('dark-theme');
      }
    }
  }

  // 执行加载内容中的脚本函数
  function executeScripts(container) {
    // 清理之前页面的事件监听器和全局状态
    cleanupPreviousPage();
    
    var scripts = container.querySelectorAll('script');
    console.log('Found scripts to execute:', scripts.length);
    
    for (var i = 0; i < scripts.length; i++) {
      var script = scripts[i];
      try {
        var newScript = document.createElement('script');
        if (script.src) {
          newScript.src = script.src;
          newScript.onload = function() {
            console.log('External script ' + i + ' loaded:', script.src);
          };
          newScript.onerror = function() {
            console.error('Failed to load external script ' + i + ':', script.src);
          };
        } else {
          newScript.textContent = script.textContent;
          console.log('Inline script ' + i + ' prepared for execution');
        }
        
        // 复制属性
        var attributes = script.attributes;
        for (var j = 0; j < attributes.length; j++) {
          var attr = attributes[j];
          newScript.setAttribute(attr.name, attr.value);
        }
        
        // 替换原脚本元素
        script.parentNode.replaceChild(newScript, script);
        
        if (!script.src) {
          console.log('Inline script ' + i + ' executed successfully');
        }
      } catch (error) {
        console.error('Error executing script ' + i + ':', error);
      }
    }
  }
  
  // 清理之前页面的状态和事件监听器
  function cleanupPreviousPage() {
    // 清理之前页面的全局状态
    if (window.pageState) {
      window.pageState.isInitialized = false;
    }
    if (window.formPageState) {
      window.formPageState.isInitialized = false;
    }
    
    // 清理定时器
    if (window.pageTimers && Array.isArray(window.pageTimers)) {
      for (var i = 0; i < window.pageTimers.length; i++) {
        var timer = window.pageTimers[i];
        clearTimeout(timer);
        clearInterval(timer);
      }
      window.pageTimers = [];
    }
    
    // 清理动态创建的元素
    var dynamicElements = document.querySelectorAll('[data-dynamic-element]');
    for (var i = 0; i < dynamicElements.length; i++) {
      dynamicElements[i].remove();
    }
    
    console.log('Previous page cleanup completed');
  }

  // Load content on initial page load
  loadContent();

  // Listen for hash changes
  window.addEventListener('hashchange', loadContent);

  // Observe changes to the body's classList to sync theme dynamically
  const observer = new MutationObserver(mutations => {
    mutations.forEach(mutation => {
      if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
        syncThemeWithContent();
      }
    });
  });

  observer.observe(document.body, { attributes: true });

  // Handle menu clicks to update hash
  document.querySelectorAll('.funi-menu-link').forEach(link => {
    link.addEventListener('click', event => {
      event.preventDefault();
      const href = link.getAttribute('href');
      if (href && href.startsWith('#')) {
        window.location.hash = href.substring(1);
      }
    });
  });
});